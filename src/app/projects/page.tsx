'use client'

import { motion } from 'framer-motion'
import { Header } from '@/components/header'
import { Footer } from '@/components/footer'
import ProjectGrid from '@/components/projects/project-grid'
import {
  RocketLaunchIcon,
  CodeBracketIcon,
  CpuChipIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

const stats = [
  { name: 'Projects Completed', value: '150+', icon: RocketLaunchIcon },
  { name: 'Technologies Used', value: '25+', icon: CpuChipIcon },
  { name: 'Lines of Code', value: '1M+', icon: CodeBracketIcon },
  { name: 'Countries Served', value: '15+', icon: GlobeAltIcon },
]

const features = [
  {
    title: 'Custom Development',
    description: 'Tailored solutions built from the ground up to meet your specific business requirements.',
    icon: CodeBracketIcon,
  },
  {
    title: 'Modern Technologies',
    description: 'We use cutting-edge technologies and frameworks to ensure your project is future-proof.',
    icon: CpuChipIcon,
  },
  {
    title: 'Global Reach',
    description: 'Our projects serve clients worldwide, from startups to enterprise-level organizations.',
    icon: GlobeAltIcon,
  },
  {
    title: 'Proven Results',
    description: 'Track record of successful project deliveries with measurable business impact.',
    icon: RocketLaunchIcon,
  },
]
export default function ProjectsPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-20 pb-16">
          <div className="container">
            <div className="text-center max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                  Our <span className="gradient-text">Projects</span>
                </h1>
                <p className="mt-6 text-lg text-gray-600 max-w-3xl mx-auto">
                  Explore our portfolio of successful projects. From innovative web applications to
                  complex enterprise solutions, see how we've helped businesses transform their digital presence.
                </p>
              </motion.div>

              {/* Stats */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mt-12"
              >
                <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
                  {stats.map((stat, index) => (
                    <motion.div
                      key={stat.name}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
                      className="text-center"
                    >
                      <div className="flex justify-center mb-2">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <stat.icon className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="text-2xl font-bold text-gray-900 sm:text-3xl">
                        {stat.value}
                      </div>
                      <div className="mt-1 text-sm text-gray-600">
                        {stat.name}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </section>
        {/* Features Section */}
        <section className="section-padding bg-gray-50">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                Why Choose Our <span className="gradient-text">Projects</span>
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                Every project we deliver is built with excellence, innovation, and your success in mind.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center">
                      <feature.icon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
        {/* Projects Grid Section */}
        <section className="section-padding">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
                Explore Our <span className="gradient-text">Portfolio</span>
              </h2>
              <p className="mt-4 text-lg text-gray-600 max-w-3xl mx-auto">
                Browse through our collection of successful projects. Use filters to find projects
                by technology, service type, or search for specific solutions.
              </p>
            </motion.div>

            <ProjectGrid
              showFilters={true}
              showSearch={true}
              variant="default"
              columns={3}
              initialProjects={[]} // Will be fetched by the component
            />
          </div>
        </section>
        {/* CTA Section */}
        <section className="section-padding bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-white sm:text-4xl">
                Ready to Start Your Project?
              </h2>
              <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                Let's discuss how we can help bring your vision to life. Our team is ready to
                tackle your next challenge and deliver exceptional results.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="#contact"
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Start a Project
                </a>
                <a
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                >
                  Get in Touch
                </a>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
