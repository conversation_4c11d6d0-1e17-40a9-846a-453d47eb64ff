'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import Link from 'next/link'
import {
  ChartBarIcon,
  UsersIcon,
  BuildingOfficeIcon,
  CogIcon,
  DocumentTextIcon,
  PhotoIcon,
  NewspaperIcon,
  StarIcon
} from '@heroicons/react/24/outline'

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'loading') return // Still loading

    if (!session) {
      router.push('/auth/signin')
      return
    }

    if (session.user.role !== 'ADMIN') {
      router.push('/')
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session || session.user.role !== 'ADMIN') {
    return null
  }

  const dashboardItems = [
    {
      title: 'Team Members',
      description: 'Manage team members and their roles',
      href: '/admin/team-members',
      icon: UsersIcon,
      color: 'bg-blue-500',
      stats: '8 members'
    },
    {
      title: 'Clients',
      description: 'Manage client information and contacts',
      href: '/admin/clients',
      icon: BuildingOfficeIcon,
      color: 'bg-green-500',
      stats: '15 clients'
    },
    {
      title: 'Projects',
      description: 'Manage projects and their status',
      href: '/admin/projects',
      icon: DocumentTextIcon,
      color: 'bg-purple-500',
      stats: '12 projects'
    },
    {
      title: 'Services',
      description: 'Manage services and categories',
      href: '/admin/services',
      icon: CogIcon,
      color: 'bg-orange-500',
      stats: '21 services'
    },
    {
      title: 'Blog Posts',
      description: 'Manage blog content and articles',
      href: '/admin/blog',
      icon: NewspaperIcon,
      color: 'bg-indigo-500',
      stats: '8 posts'
    },
    {
      title: 'Testimonials',
      description: 'Manage client testimonials',
      href: '/admin/testimonials',
      icon: StarIcon,
      color: 'bg-yellow-500',
      stats: '10 reviews'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Admin Dashboard
              </h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                Welcome back, {session.user.name || session.user.email}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors"
              >
                View Website
              </Link>
              <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {session.user.name?.charAt(0) || session.user.email?.charAt(0)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dashboardItems.map((item) => {
            const Icon = item.icon
            return (
              <Link
                key={item.href}
                href={item.href}
                className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:scale-[1.02]"
              >
                <div className="p-6">
                  <div className="flex items-center">
                    <div className={`${item.color} rounded-lg p-3`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {item.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {item.description}
                      </p>
                    </div>
                  </div>
                  <div className="mt-4 flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {item.stats}
                    </span>
                    <svg
                      className="h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>

        {/* Quick Stats */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Quick Overview
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                15
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Active Clients
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                12
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Ongoing Projects
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                8
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Team Members
              </div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                21
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Services Offered
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
