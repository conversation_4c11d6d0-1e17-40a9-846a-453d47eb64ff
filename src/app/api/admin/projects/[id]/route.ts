import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/projects/[id] - Get a specific project
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  const project = await prisma.projects.findUnique({
    where: { id },
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true,
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true,
          ordertotalamount: true,
        },
      },
      teammembers: {
        select: {
          id: true,
          name: true,
        },
      },
      tasks: {
        select: {
          id: true,
          status: true,
        },
        take: 10,
      },
      projectdocuments: {
        select: {
          id: true,
          filename: true,
          fileurl: true,
          filetype: true,
          filesize: true,
          createdat: true,
        },
        take: 10,
      },
      messages: {
        select: {
          id: true,
          content: true,
          sendername: true,
          createdat: true,
        },
        take: 10,
        orderBy: {
          createdat: 'desc',
        },
      },
      feedbacks: {
        select: {
          id: true,
          rating: true,
          comment: true,
          createdat: true,
          clients: {
            select: {
              contactname: true,
              companyname: true,
            },
          },
        },
      },
      _count: {
        select: {
          tasks: true,
          projectdocuments: true,
          messages: true,
          feedbacks: true,
        },
      },
    },
  })

  if (!project) {
    return NextResponse.json(
      { success: false, error: 'Project not found' },
      { status: 404 }
    )
  }

  return successResponse(project)
})

// PUT /api/admin/projects/[id] - Update a project
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.project.update)
  const data = await validate(request)

  // Check if project exists
  const existingProject = await prisma.projects.findUnique({
    where: { id },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404)
  }

  // Validate order exists if provided
  if (data.orderid && data.orderid !== existingProject.orderid) {
    const order = await prisma.orders.findUnique({
      where: { id: data.orderid },
    })

    if (!order) {
      throw new ApiError('Order not found', 400)
    }
  }

  // Validate client exists if provided
  if (data.clientid && data.clientid !== existingProject.clientid) {
    const client = await prisma.clients.findUnique({
      where: { id: data.clientid },
    })

    if (!client) {
      throw new ApiError('Client not found', 400)
    }
  }

  const projectData = { ...data }

  const project = await prisma.projects.update({
    where: { id },
    data: projectData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true,
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true,
          ordertotalamount: true,
        },
      },
      teammembers: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          tasks: true,
          projectdocuments: true,
          messages: true,
        },
      },
    },
  })

  return successResponse(project, 'Project updated successfully')
})

// DELETE /api/admin/projects/[id] - Delete a project
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  // Check if project exists
  const existingProject = await prisma.projects.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          tasks: true,
          documents: true,
          messages: true,
        },
      },
    },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404)
  }

  // Check if project has related data
  const hasRelatedData = existingProject._count.tasks > 0 ||
                        existingProject._count.documents > 0 ||
                        existingProject._count.messages > 0

  if (hasRelatedData) {
    throw new ApiError('Cannot delete project with related tasks, documents, or messages. Please remove them first.', 400)
  }

  await prisma.projects.delete({
    where: { id },
  })

  return successResponse(null, 'Project deleted successfully')
})

// PATCH /api/admin/projects/[id] - Partial update (e.g., toggle status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const body = await request.json()

  // Check if project exists
  const existingProject = await prisma.projects.findUnique({
    where: { id },
  })

  if (!existingProject) {
    throw new ApiError('Project not found', 404)
  }

  // Only allow specific fields for PATCH
  const allowedFields = ['status', 'isfeatured', 'ispublic', 'displayorder']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      if (field === 'isfeatured' || field === 'ispublic') {
        updateData[field] = body[field] === true || body[field] === 'true'
      } else if (field === 'displayorder') {
        updateData[field] = parseInt(body[field])
      } else {
        updateData[field] = body[field]
      }
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const project = await prisma.projects.update({
    where: { id },
    data: updateData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
        },
      },
      _count: {
        select: {
          tasks: true,
          projectdocuments: true,
          messages: true,
        },
      },
    },
  })

  return successResponse(project, 'Project updated successfully')
})
