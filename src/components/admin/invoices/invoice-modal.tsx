'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { XMarkIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline'

interface InvoiceItem {
  id?: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface Invoice {
  id?: string
  clientId: string
  projectId?: string
  orderId?: string
  contractId?: string
  invoiceNumber: string
  description?: string
  subtotal: number
  taxAmount: number
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  items?: InvoiceItem[]
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  title: string
  initialData?: Invoice
}

interface DropdownOption {
  value: string
  label: string
}

export default function InvoiceModal({
  isOpen,
  onClose,
  onSuccess,
  title,
  initialData
}: InvoiceModalProps) {
  const [formData, setFormData] = useState<Omit<Invoice, 'items'>>({
    clientId: '',
    projectId: '',
    orderId: '',
    contractId: '',
    invoiceNumber: '',
    description: '',
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0,
    status: 'DRAFT',
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  })

  const [items, setItems] = useState<InvoiceItem[]>([
    { description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }
  ])

  const [clients, setClients] = useState<DropdownOption[]>([])
  const [projects, setProjects] = useState<DropdownOption[]>([])
  const [orders, setOrders] = useState<DropdownOption[]>([])
  const [contracts, setContracts] = useState<DropdownOption[]>([])
  const [loading, setLoading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset form when modal opens
  const resetForm = () => {
    setFormData({
      clientId: '',
      projectId: '',
      orderId: '',
      contractId: '',
      invoiceNumber: '',
      description: '',
      subtotal: 0,
      taxAmount: 0,
      totalAmount: 0,
      status: 'DRAFT',
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    })
    setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
  }

  useEffect(() => {
    if (isOpen) {
      fetchDropdownData()

      if (initialData) {
        // Load existing invoice data for editing
        console.log('Loading invoice for edit:', initialData)

        // Separate items from the rest of the form data
        const { items: initialItems, ...restOfData } = initialData

        // Set form data with proper date formatting
        setFormData({
          ...restOfData,
          issueDate: restOfData.issueDate ? new Date(restOfData.issueDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
          dueDate: restOfData.dueDate ? new Date(restOfData.dueDate).toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          paidAt: restOfData.paidAt ? new Date(restOfData.paidAt).toISOString().split('T')[0] : undefined
        })

        // Load existing items or create default item
        if (initialItems && initialItems.length > 0) {
          const normalizedItems = initialItems.map(item => ({
            ...item,
            quantity: Number(item.quantity) || 1,
            unitPrice: Number(item.unitPrice) || 0,
            totalPrice: Number(item.totalPrice) || 0,
          }))
          setItems(normalizedItems)
        } else {
          setItems([{ description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
        }
      } else {
        // Reset form for new invoice
        resetForm()
      }
    }
  }, [isOpen, initialData])

  const fetchDropdownData = async () => {
    setLoading(true)
    try {
      const [clientsRes, projectsRes, ordersRes, contractsRes] = await Promise.all([
        fetch('/api/admin/clients/dropdown'),
        fetch('/api/admin/projects/dropdown'),
        fetch('/api/admin/orders/dropdown'),
        fetch('/api/admin/contracts/dropdown')
      ])

      const [clientsData, projectsData, ordersData, contractsData] = await Promise.all([
        clientsRes.json(),
        projectsRes.json(),
        ordersRes.json(),
        contractsRes.json()
      ])

      if (clientsData.success) setClients(clientsData.data)
      if (projectsData.success) setProjects(projectsData.data)
      if (ordersData.success) setOrders(ordersData.data)
      if (contractsData.success) setContracts(contractsData.data)
    } catch (error) {
      console.error('Error fetching dropdown data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotals = (updatedItems: InvoiceItem[]) => {
    const subtotal = updatedItems.reduce((sum, item) => sum + (Number(item.totalPrice) || 0), 0)
    const totalAmount = subtotal + (Number(formData.taxAmount) || 0)

    setFormData(prev => ({
      ...prev,
      subtotal,
      totalAmount
    }))
  }

  const updateItem = (index: number, field: keyof InvoiceItem, value: string | number) => {
    const updatedItems = [...items]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    // Recalculate total price for this item
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = Number(updatedItems[index].quantity) || 0
      const unitPrice = Number(updatedItems[index].unitPrice) || 0
      updatedItems[index].totalPrice = quantity * unitPrice
    }

    setItems(updatedItems)
    calculateTotals(updatedItems)
  }

  const addItem = () => {
    setItems([...items, { description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
  }

  const removeItem = (index: number) => {
    if (items.length > 1) {
      const updatedItems = items.filter((_, i) => i !== index)
      setItems(updatedItems)
      calculateTotals(updatedItems)
    }
  }

  const handleTaxChange = (taxAmount: number) => {
    const totalAmount = formData.subtotal + taxAmount
    setFormData(prev => ({
      ...prev,
      taxAmount,
      totalAmount
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    let submitData: any = null // Declare outside try block for error logging

    try {
      // Debug: Log current form state
      console.log('Current formData:', formData)
      console.log('Current items:', items)

      // Check if formData is properly initialized
      if (!formData || typeof formData !== 'object') {
        throw new Error('Form data is not properly initialized')
      }

      // Validate required fields
      if (!formData.clientId || formData.clientId.trim() === '') {
        throw new Error('Please select a client')
      }

      // Auto-generate invoice number if empty
      let invoiceNumber = formData.invoiceNumber.trim()
      if (!invoiceNumber) {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0')
        invoiceNumber = `INV-${year}${month}${day}-${time}`
      }
      if (!formData.issueDate) {
        throw new Error('Please select an issue date')
      }
      if (!formData.dueDate) {
        throw new Error('Please select a due date')
      }

      // Filter and validate items
      const validItems = items.filter(item => item.description.trim() !== '')
      if (validItems.length === 0) {
        throw new Error('Please add at least one invoice item with a description')
      }

      submitData = {
        ...formData,
        invoiceNumber, // Use the generated or provided invoice number
        // Ensure numeric fields are properly formatted
        subtotal: Number(formData.subtotal) || 0,
        taxAmount: Number(formData.taxAmount) || 0,
        totalAmount: Number(formData.totalAmount) || 0,
        items: validItems.map(item => ({
          ...item,
          quantity: Number(item.quantity) || 1,
          unitPrice: Number(item.unitPrice) || 0,
          totalPrice: Number(item.totalPrice) || 0,
        }))
      }

      // Validate submitData is not empty
      console.log('Final submitData before sending:', submitData)
      if (!submitData || Object.keys(submitData).length === 0) {
        throw new Error('Submit data is empty - form not properly initialized')
      }

      // Determine if we're creating or updating
      const isEditing = initialData && initialData.id
      const url = isEditing
        ? `/api/admin/invoices/${initialData.id}`
        : '/api/admin/invoices'
      const method = isEditing ? 'PUT' : 'POST'

      console.log(`${isEditing ? 'Updating' : 'Creating'} invoice:`, { url, method, submitData })

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submitData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.message || `HTTP ${response.status}: ${response.statusText}`
        throw new Error(errorMessage)
      }

      onSuccess()
    } catch (error) {
      console.error('Error saving invoice:', error)
      console.error('Submit data:', submitData)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      alert(`Failed to save invoice: ${errorMessage}`)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-white">{title}</h2>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-white hover:text-gray-200 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Number *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData?.invoiceNumber || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="INV-2024-001"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="DRAFT">Draft</option>
                      <option value="SENT">Sent</option>
                      <option value="PAID">Paid</option>
                      <option value="OVERDUE">Overdue</option>
                      <option value="CANCELLED">Cancelled</option>
                    </select>
                  </div>
                </div>

                {/* Client and Related */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Client *
                    </label>
                    <select
                      required
                      value={formData?.clientId || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, clientId: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      disabled={loading}
                    >
                      <option value="">Select a client</option>
                      {clients.map((client) => (
                        <option key={client.value} value={client.value}>
                          {client.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Project
                    </label>
                    <select
                      value={formData.projectId || ''}
                      onChange={(e) => setFormData({ ...formData, projectId: e.target.value || undefined })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      disabled={loading}
                    >
                      <option value="">Select a project (optional)</option>
                      {projects.map((project) => (
                        <option key={project.value} value={project.value}>
                          {project.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Dates */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issue Date *
                    </label>
                    <input
                      type="date"
                      required
                      value={formData.issueDate}
                      onChange={(e) => setFormData({ ...formData, issueDate: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date *
                    </label>
                    <input
                      type="date"
                      required
                      value={formData.dueDate}
                      onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    rows={2}
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Invoice description or notes"
                  />
                </div>

                {/* Invoice Items */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Invoice Items</h3>
                    <button
                      type="button"
                      onClick={addItem}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Item
                    </button>
                  </div>

                  <div className="space-y-3">
                    {items.map((item, index) => (
                      <div key={index} className="grid grid-cols-12 gap-3 items-end">
                        <div className="col-span-5">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Description
                          </label>
                          <input
                            type="text"
                            value={item.description}
                            onChange={(e) => updateItem(index, 'description', e.target.value)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Item description"
                          />
                        </div>
                        <div className="col-span-2">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Quantity
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItem(index, 'quantity', Number(e.target.value) || 1)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div className="col-span-2">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Unit Price
                          </label>
                          <input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateItem(index, 'unitPrice', Number(e.target.value) || 0)}
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div className="col-span-2">
                          <label className="block text-xs font-medium text-gray-700 mb-1">
                            Total
                          </label>
                          <input
                            type="text"
                            value={`$${(Number(item.totalPrice) || 0).toFixed(2)}`}
                            readOnly
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-gray-50"
                          />
                        </div>
                        <div className="col-span-1">
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            disabled={items.length === 1}
                            className="p-2 text-gray-400 hover:text-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Totals */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm font-medium text-gray-700">Subtotal:</span>
                      <span className="text-sm font-medium text-gray-900">
                        ${(Number(formData.subtotal) || 0).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <label className="text-sm font-medium text-gray-700">Tax Amount:</label>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm">$</span>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.taxAmount}
                          onChange={(e) => handleTaxChange(Number(e.target.value) || 0)}
                          className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>
                    <div className="flex justify-between border-t pt-3">
                      <span className="text-lg font-bold text-gray-900">Total Amount:</span>
                      <span className="text-lg font-bold text-gray-900">
                        ${(Number(formData.totalAmount) || 0).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>



                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 pt-6 border-t">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || loading}
                    className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-md hover:from-blue-700 hover:to-blue-800 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                        Saving...
                      </>
                    ) : (
                      initialData ? 'Update Invoice' : 'Create Invoice'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
